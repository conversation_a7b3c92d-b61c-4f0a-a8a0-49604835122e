/* ==================== 全局样式 ==================== */

/* 隐藏滚动条 */
::-webkit-scrollbar {
  display: none;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  scrollbar-width: none;
  -ms-overflow-style: none;
  background-color: #f43a47;
  font-family: 'Inter', 'Helvetica', 'Arial', sans-serif;
  overflow-x: hidden;
  position: relative;
  width: 100%;
  scroll-behavior: smooth;
  user-select: none;
  cursor: none;
  color: #000;
}

/* ==================== 滚动容器和页面系统 ==================== */
.scroll-container {
  height: 100vh;
  overflow-y: auto;
  scroll-behavior: smooth;
}

.page-section {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  transition: all 0.8s cubic-bezier(0.23, 1, 0.32, 1);
}

.page-section:not(.active-section) {
  opacity: 0;
  transform: translateY(50px);
  pointer-events: none;
}

.page-section.active-section {
  opacity: 1;
  transform: translateY(0);
  pointer-events: all;
}

/* ==================== 移动端提示 ==================== */
.mobile-warning {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #f43a47;
  z-index: 99999;
  justify-content: center;
  align-items: center;
  text-align: center;
}

.mobile-warning-text {
  color: white;
  font-family: 'Inter', 'Helvetica', 'Arial', sans-serif;
  font-size: 4vw;
  font-weight: bold;
  padding: 20px;
  line-height: 1.4;
}

@media screen and (max-width: 768px), (max-height: 500px) {
  .mobile-warning {
    display: flex !important;
  }
  .main-content {
    display: none !important;
  }
}

/* ==================== 自定义光标 ==================== */
.custom-cursor {
  position: fixed;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  left: 0;
  top: 0;
  background: #fff;
  margin-left: -10px;
  margin-top: -10px;
  mix-blend-mode: difference;
  z-index: 2000;
  pointer-events: none;
  transition: transform 0.1s ease;
}

/* ==================== 加载动画 ==================== */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #f43a47;
  z-index: 9999;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: opacity 0.5s ease-out;
}

.loading-overlay.fade-out {
  opacity: 0;
  pointer-events: none;
}

.loading-container {
  text-align: center;
  color: #000;
}

.loading-text {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 30px;
  letter-spacing: 3px;
}

.loading-bar {
  width: 200px;
  height: 2px;
  background-color: rgba(0, 0, 0, 0.2);
  margin: 0 auto 20px;
  overflow: hidden;
}

.loading-progress {
  height: 100%;
  background-color: #000;
  width: 0%;
  transition: width 0.3s ease;
}

.loading-percentage {
  font-size: 1.2rem;
  font-weight: 500;
}

/* ==================== 导航栏 ==================== */
.navbar {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  padding: 30px 50px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  z-index: 1000;
  background: rgba(244, 58, 71, 0.9);
  backdrop-filter: blur(10px);
}

.nav-logo .logo-text {
  font-size: 1.5rem;
  font-weight: 700;
  color: #000;
  letter-spacing: 2px;
}

.nav-links {
  display: flex;
  gap: 40px;
}

.nav-link {
  color: #000;
  text-decoration: none;
  font-weight: 500;
  font-size: 1rem;
  letter-spacing: 1px;
  transition: opacity 0.3s ease;
  position: relative;
}

.nav-link:hover,
.nav-link.active {
  opacity: 0.7;
}

.nav-link.active::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: #000;
}

/* ==================== 主页样式 ==================== */
.hero-section {
  position: relative;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

/* ==================== X OBERON 水印系统 ==================== */
.watermark-text {
  position: absolute;
  font-size: clamp(8rem, 18vw, 22rem);
  font-weight: 900;
  color: rgba(0, 0, 0, 0.08);
  font-family: 'Inter', 'Helvetica', sans-serif;
  letter-spacing: 0.1em;
  user-select: none;
  pointer-events: none;
  white-space: nowrap;
}

.watermark-1 {
  top: 15%;
  left: -5%;
  transform: rotate(-12deg);
  animation: watermarkFloat1 8s ease-in-out infinite;
}

.watermark-2 {
  top: 50%;
  right: -8%;
  transform: rotate(8deg) translateY(-50%);
  animation: watermarkFloat2 10s ease-in-out infinite reverse;
  opacity: 0.06;
}

.watermark-3 {
  bottom: 15%;
  left: 25%;
  transform: rotate(-5deg);
  animation: watermarkFloat3 12s ease-in-out infinite;
  opacity: 0.04;
  font-size: clamp(6rem, 14vw, 18rem);
}

@keyframes watermarkFloat1 {
  0%, 100% {
    transform: rotate(-12deg) translateY(0px);
    opacity: 0.08;
  }
  50% {
    transform: rotate(-12deg) translateY(-15px);
    opacity: 0.12;
  }
}

@keyframes watermarkFloat2 {
  0%, 100% {
    transform: rotate(8deg) translateY(-50%) translateX(0px);
    opacity: 0.06;
  }
  50% {
    transform: rotate(8deg) translateY(-50%) translateX(10px);
    opacity: 0.09;
  }
}

@keyframes watermarkFloat3 {
  0%, 100% {
    transform: rotate(-5deg) scale(1);
    opacity: 0.04;
  }
  50% {
    transform: rotate(-5deg) scale(1.03);
    opacity: 0.07;
  }
}

.hero-content {
  position: relative;
  z-index: 2;
  text-align: center;
  max-width: 800px;
  padding: 0 20px;
}

.main-title {
  margin-bottom: 30px;
}

.title-line {
  display: block;
  font-size: clamp(3rem, 8vw, 8rem);
  font-weight: 900;
  line-height: 0.9;
  margin-bottom: 10px;
}

.title-line.highlight {
  color: #fff;
  text-shadow: 2px 2px 0px #000;
}

.hero-subtitle {
  margin-bottom: 50px;
}

.hero-subtitle p {
  font-size: 1.2rem;
  font-weight: 400;
  opacity: 0.8;
  letter-spacing: 1px;
}

.cta-button {
  background-color: #000;
  color: #f43a47;
  border: none;
  padding: 20px 40px;
  font-size: 1rem;
  font-weight: 600;
  letter-spacing: 2px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
}

.cta-button:hover {
  background-color: #fff;
  color: #000;
  transform: translateY(-2px);
}

.scroll-indicator {
  position: absolute;
  bottom: 50px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
}

.scroll-line {
  width: 2px;
  height: 50px;
  background-color: #000;
  animation: scrollPulse 2s infinite;
}

.scroll-text {
  font-size: 0.8rem;
  font-weight: 500;
  letter-spacing: 2px;
  writing-mode: vertical-rl;
  text-orientation: mixed;
}

@keyframes scrollPulse {
  0%, 100% { opacity: 0.3; }
  50% { opacity: 1; }
}

/* ==================== 项目区域 ==================== */
.projects-section {
  padding: 100px 50px;
  background-color: #fff;
  color: #000;
}

.section-header {
  text-align: center;
  margin-bottom: 80px;
}

.section-title {
  font-size: 3rem;
  font-weight: 900;
  margin-bottom: 20px;
  letter-spacing: 2px;
}

.section-line {
  width: 100px;
  height: 3px;
  background-color: #f43a47;
  margin: 0 auto;
}

.projects-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 50px;
  max-width: 1200px;
  margin: 0 auto;
}

.project-card {
  background-color: #f8f8f8;
  border-radius: 10px;
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  cursor: pointer;
}

.project-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.project-image {
  height: 250px;
  background: linear-gradient(135deg, #f43a47, #ff6b7a);
  display: flex;
  align-items: center;
  justify-content: center;
}

.project-placeholder {
  color: #fff;
  font-size: 2rem;
  font-weight: 700;
  letter-spacing: 2px;
}

.project-info {
  padding: 30px;
}

.project-title {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 15px;
}

.project-description {
  font-size: 1rem;
  line-height: 1.6;
  margin-bottom: 20px;
  opacity: 0.8;
}

.project-tech {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.tech-tag {
  background-color: #f43a47;
  color: #fff;
  padding: 5px 15px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
}

/* ==================== 联系区域 ==================== */
.contact-section {
  padding: 100px 50px;
  background-color: #000;
  color: #fff;
  text-align: center;
}

.contact-title {
  font-size: 3rem;
  font-weight: 900;
  margin-bottom: 20px;
  letter-spacing: 2px;
}

.contact-subtitle {
  font-size: 1.2rem;
  margin-bottom: 50px;
  opacity: 0.8;
}

.contact-buttons {
  display: flex;
  gap: 30px;
  justify-content: center;
  flex-wrap: wrap;
}

.contact-button {
  display: inline-block;
  padding: 20px 40px;
  background-color: #f43a47;
  color: #fff;
  text-decoration: none;
  font-weight: 600;
  letter-spacing: 2px;
  text-transform: uppercase;
  transition: all 0.3s ease;
}

.contact-button:hover {
  background-color: #fff;
  color: #000;
  transform: translateY(-2px);
}

.contact-button.secondary {
  background-color: transparent;
  border: 2px solid #fff;
  color: #fff;
}

.contact-button.secondary:hover {
  background-color: #fff;
  color: #000;
}

/* ==================== 关于页面样式 ==================== */
.about-hero {
  position: relative;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.about-content {
  position: relative;
  z-index: 2;
  text-align: center;
  max-width: 800px;
  padding: 0 20px;
}

.about-title {
  margin-bottom: 40px;
}

.about-description {
  font-size: 1.3rem;
  line-height: 1.8;
  font-weight: 400;
  opacity: 0.9;
  max-width: 600px;
  margin: 0 auto;
}

/* ==================== 技能区域 ==================== */
.skills-section {
  padding: 100px 50px;
  background-color: #fff;
  color: #000;
}

.skills-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 50px;
  max-width: 1200px;
  margin: 0 auto;
}

.skill-category {
  background-color: #f8f8f8;
  padding: 40px;
  border-radius: 10px;
}

.skill-title {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 30px;
  color: #f43a47;
}

.skill-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.skill-item {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.skill-name {
  font-weight: 600;
  font-size: 1rem;
}

.skill-bar {
  width: 100%;
  height: 8px;
  background-color: #e0e0e0;
  border-radius: 4px;
  overflow: hidden;
}

.skill-progress {
  height: 100%;
  background: linear-gradient(90deg, #f43a47, #ff6b7a);
  width: 0%;
  transition: width 1s ease-in-out;
  border-radius: 4px;
}

/* ==================== 时间线样式 ==================== */
.timeline-section {
  padding: 100px 50px;
  background-color: #f8f8f8;
  color: #000;
}

.timeline {
  max-width: 800px;
  margin: 0 auto;
  position: relative;
}

.timeline::before {
  content: '';
  position: absolute;
  left: 50%;
  top: 0;
  bottom: 0;
  width: 2px;
  background-color: #f43a47;
  transform: translateX(-50%);
}

.timeline-item {
  display: flex;
  margin-bottom: 50px;
  position: relative;
}

.timeline-item:nth-child(odd) {
  flex-direction: row;
}

.timeline-item:nth-child(even) {
  flex-direction: row-reverse;
}

.timeline-date {
  flex: 0 0 150px;
  text-align: center;
  font-weight: 700;
  color: #f43a47;
  font-size: 1.1rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.timeline-content {
  flex: 1;
  background-color: #fff;
  padding: 30px;
  border-radius: 10px;
  margin: 0 30px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  position: relative;
}

.timeline-item:nth-child(odd) .timeline-content::before {
  content: '';
  position: absolute;
  left: -10px;
  top: 50%;
  transform: translateY(-50%);
  width: 0;
  height: 0;
  border-top: 10px solid transparent;
  border-bottom: 10px solid transparent;
  border-right: 10px solid #fff;
}

.timeline-item:nth-child(even) .timeline-content::before {
  content: '';
  position: absolute;
  right: -10px;
  top: 50%;
  transform: translateY(-50%);
  width: 0;
  height: 0;
  border-top: 10px solid transparent;
  border-bottom: 10px solid transparent;
  border-left: 10px solid #fff;
}

.timeline-title {
  font-size: 1.3rem;
  font-weight: 700;
  margin-bottom: 10px;
  color: #000;
}

.timeline-company {
  font-size: 1rem;
  font-weight: 600;
  color: #f43a47;
  margin-bottom: 15px;
}

.timeline-description {
  font-size: 1rem;
  line-height: 1.6;
  opacity: 0.8;
}

/* ==================== 响应式设计 ==================== */
@media (max-width: 768px) {
  .navbar {
    padding: 20px 30px;
  }

  .nav-links {
    gap: 20px;
  }

  .hero-content,
  .about-content {
    padding: 0 30px;
  }

  .title-line {
    font-size: clamp(2rem, 10vw, 4rem);
  }

  .projects-section,
  .skills-section,
  .timeline-section,
  .contact-section {
    padding: 80px 30px;
  }

  .projects-grid {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  .skills-grid {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  .contact-buttons {
    flex-direction: column;
    align-items: center;
  }

  .timeline::before {
    left: 30px;
  }

  .timeline-item {
    flex-direction: column !important;
    margin-left: 60px;
  }

  .timeline-date {
    position: absolute;
    left: -90px;
    top: 0;
    width: 80px;
    font-size: 0.9rem;
  }

  .timeline-content {
    margin: 0;
  }

  .timeline-content::before {
    left: -10px !important;
    right: auto !important;
    border-right: 10px solid #fff !important;
    border-left: none !important;
  }
}

/* ==================== 第2页 - 关于页面 ==================== */
#page2 {
  background-color: #fff;
  color: #000;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 0 2.5vw;
  position: relative;
}

.about-content {
  max-width: 800px;
  padding: 0 50px;
  text-align: center;
  z-index: 10;
  position: relative;
}

.about-title {
  font-size: clamp(3rem, 8vw, 8rem);
  font-weight: 900;
  margin-bottom: 50px;
  color: #f43a47;
}

.about-line {
  font-size: 1.5rem;
  line-height: 1.6;
  margin-bottom: 30px;
}

.normal {
  color: #000;
  font-weight: 400;
}

.highlight {
  color: #f43a47;
  font-weight: 600;
  margin: 0 10px;
}

.about-paragraph {
  font-size: 1.2rem;
  line-height: 1.8;
  font-weight: 300;
  opacity: 0.8;
}

/* ==================== 简单装饰元素 ==================== */
.page-decoration {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.deco-dot {
  position: absolute;
  width: 8px;
  height: 8px;
  background-color: #f43a47;
  border-radius: 50%;
  opacity: 0.3;
  animation: pulse 3s ease-in-out infinite;
}

.dot-1 {
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.dot-2 {
  bottom: 30%;
  right: 15%;
  animation-delay: 1s;
}

.deco-line {
  position: absolute;
  background-color: #f43a47;
  opacity: 0.2;
}

.line-1 {
  width: 2px;
  height: 100px;
  top: 15%;
  right: 8%;
  animation: slideUp 4s ease-in-out infinite;
}

.floating-dot {
  position: absolute;
  width: 6px;
  height: 6px;
  background-color: #f43a47;
  border-radius: 50%;
  opacity: 0.4;
  animation: float 5s ease-in-out infinite;
}

.floating-dot.dot-1 {
  top: 25%;
  left: 15%;
  animation-delay: 0s;
}

.floating-dot.dot-2 {
  top: 60%;
  right: 20%;
  animation-delay: 1.5s;
}

.floating-dot.dot-3 {
  bottom: 25%;
  left: 25%;
  animation-delay: 3s;
}

.deco-circle {
  position: absolute;
  width: 40px;
  height: 40px;
  border: 2px solid #000;
  border-radius: 50%;
  opacity: 0.1;
  top: 30%;
  right: 10%;
  animation: rotate 8s linear infinite;
}

.deco-square {
  position: absolute;
  width: 30px;
  height: 30px;
  background-color: #000;
  opacity: 0.1;
  bottom: 20%;
  left: 8%;
  animation: pulse 4s ease-in-out infinite;
}

.code-symbol {
  position: absolute;
  font-size: 2rem;
  font-weight: 700;
  color: #f43a47;
  opacity: 0.2;
  font-family: 'Monaco', monospace;
}

.symbol-1 {
  top: 20%;
  left: 10%;
  animation: float 6s ease-in-out infinite;
}

.symbol-2 {
  top: 60%;
  right: 15%;
  animation: float 6s ease-in-out infinite;
  animation-delay: 2s;
}

.symbol-3 {
  bottom: 25%;
  left: 20%;
  animation: float 6s ease-in-out infinite;
  animation-delay: 4s;
}

@keyframes pulse {
  0%, 100% { opacity: 0.2; transform: scale(1); }
  50% { opacity: 0.5; transform: scale(1.1); }
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-15px); }
}

@keyframes slideUp {
  0%, 100% { transform: translateY(0); opacity: 0.2; }
  50% { transform: translateY(-20px); opacity: 0.4; }
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* ==================== 第3页 - 身份展示 ==================== */
#page3 {
  background-color: #f43a47;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.identity-text-group {
  text-align: center;
  z-index: 10;
  position: relative;
}

.identity-title {
  font-size: clamp(3rem, 8vw, 12rem);
  font-weight: 900;
  color: #000;
  line-height: 0.9;
  margin-bottom: 2vh;
  opacity: 0;
  transform: translateY(100px);
  transition: all 1s cubic-bezier(0.4, 0, 0.2, 1);
}

.identity-title.animate {
  opacity: 1;
  transform: translateY(0);
}

/* ==================== 第4页 - 创作者 ==================== */
#page4 {
  background-color: #fff;
  position: relative;
  overflow: hidden;
}

.whoami-title {
  position: absolute;
  left: 2vw;
  top: 15vh;
  font-size: clamp(2rem, 6vw, 8rem);
  font-weight: 900;
  color: #000;
  z-index: 1000;
}

.page-link {
  position: absolute;
  z-index: 1000;
  cursor: pointer;
  transition: all 0.3s ease;
}

.page-link:hover {
  transform: translateX(10px);
}

.page-quote {
  font-weight: 900;
  line-height: 0.8;
}

/* ==================== 第5页 - 开发者 ==================== */
#page5 {
  background-color: #000;
  position: relative;
  overflow: hidden;
}

#code-canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
  opacity: 0.3;
}

.whoami-title-right {
  position: absolute;
  right: 2vw;
  top: 15vh;
  font-size: clamp(2rem, 6vw, 8rem);
  font-weight: 900;
  color: #f43a47;
  z-index: 1000;
  text-align: right;
}

.page-link-right {
  position: absolute;
  right: 2vw;
  top: 28vh;
  color: #f43a47;
  font-size: 1.8vh;
  z-index: 1000;
  cursor: pointer;
  transition: all 0.3s ease;
}

.page-link-right:hover {
  transform: translateX(-10px);
}

.page-quote-right {
  position: absolute;
  left: 50vw;
  top: 52vh;
  color: #fff;
  font-size: 3.8vw;
  text-align: center;
  z-index: 1001;
  font-weight: 900;
  line-height: 0.8;
}

/* ==================== 动画效果 ==================== */
@keyframes particleFloat {
  0%, 100% {
    transform: translateY(0px) translateX(0px);
    opacity: 0.2;
  }
  25% {
    transform: translateY(-20px) translateX(10px);
    opacity: 0.5;
  }
  50% {
    transform: translateY(-10px) translateX(-15px);
    opacity: 0.8;
  }
  75% {
    transform: translateY(-30px) translateX(5px);
    opacity: 0.3;
  }
}

.developer-link:hover .link-arrow {
  transform: translateX(5px);
}
